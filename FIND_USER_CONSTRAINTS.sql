-- =============================================
-- Script to find ALL foreign key constraints referencing User table
-- This will help identify what's still blocking the TRUNCATE
-- =============================================

USE [CabYaari]
GO

PRINT '=== FINDING ALL FOREIGN KEY CONSTRAINTS REFERENCING USER TABLE ==='
PRINT ''

-- Find all foreign key constraints that reference the User table
SELECT 
    fk.name AS ForeignKeyName,
    SCHEMA_NAME(fk.schema_id) AS ForeignKeySchema,
    OBJECT_NAME(fk.parent_object_id) AS ReferencingTable,
    SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) AS ReferencingSchema,
    COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS ReferencingColumn,
    SCHEMA_NAME(pk.schema_id) AS ReferencedSchema,
    OBJECT_NAME(pk.object_id) AS ReferencedTable,
    COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS ReferencedColumn,
    CASE 
        WHEN fk.delete_referential_action = 0 THEN 'NO ACTION'
        WHEN fk.delete_referential_action = 1 THEN 'CASCADE'
        WHEN fk.delete_referential_action = 2 THEN 'SET NULL'
        WHEN fk.delete_referential_action = 3 THEN 'SET DEFAULT'
    END AS DeleteAction,
    fk.is_disabled AS IsDisabled
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.objects pk ON fkc.referenced_object_id = pk.object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')
ORDER BY ReferencingSchema, ReferencingTable;

PRINT ''
PRINT '=== CHECKING FOR DATA IN REMAINING REFERENCING TABLES ==='
PRINT ''

-- Check if there's still data in tables that reference User
DECLARE @sql NVARCHAR(MAX) = ''
DECLARE @tableName NVARCHAR(128)
DECLARE @schemaName NVARCHAR(128)
DECLARE @columnName NVARCHAR(128)

DECLARE constraint_cursor CURSOR FOR
SELECT 
    SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) AS ReferencingSchema,
    OBJECT_NAME(fk.parent_object_id) AS ReferencingTable,
    COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS ReferencingColumn
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')

OPEN constraint_cursor
FETCH NEXT FROM constraint_cursor INTO @schemaName, @tableName, @columnName

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @sql = 'SELECT ''' + @schemaName + '.' + @tableName + ''' AS TableName, COUNT(*) AS RowCount FROM [' + @schemaName + '].[' + @tableName + '] WHERE [' + @columnName + '] IS NOT NULL'
    EXEC sp_executesql @sql
    
    FETCH NEXT FROM constraint_cursor INTO @schemaName, @tableName, @columnName
END

CLOSE constraint_cursor
DEALLOCATE constraint_cursor

PRINT ''
PRINT '=== SOLUTION: DELETE FROM ALL REFERENCING TABLES ==='
PRINT 'Run the following commands to clear all referencing tables:'
PRINT ''

-- Generate DELETE statements for all referencing tables
DECLARE delete_cursor CURSOR FOR
SELECT DISTINCT
    SCHEMA_NAME(OBJECTPROPERTY(fk.parent_object_id, 'SchemaId')) AS ReferencingSchema,
    OBJECT_NAME(fk.parent_object_id) AS ReferencingTable
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')

OPEN delete_cursor
FETCH NEXT FROM delete_cursor INTO @schemaName, @tableName

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'DELETE FROM [' + @schemaName + '].[' + @tableName + '];'
    FETCH NEXT FROM delete_cursor INTO @schemaName, @tableName
END

CLOSE delete_cursor
DEALLOCATE delete_cursor

PRINT ''
PRINT 'TRUNCATE TABLE [Identity].[User];'
PRINT ''
PRINT '=== END OF GENERATED COMMANDS ==='
