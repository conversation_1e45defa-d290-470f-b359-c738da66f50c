-- =============================================
-- Script to safely clear User table and related data
-- Resolves foreign key constraint issues when truncating User table
-- =============================================

USE [CabYaari]
GO

PRINT 'Starting User table cleanup process...'
PRINT ''

-- =============================================
-- SOLUTION 1: DELETE IN CORRECT ORDER (RECOMMENDED)
-- =============================================

PRINT '=== SOLUTION 1: Delete in correct order ==='
PRINT 'This preserves referential integrity and is the safest approach'
PRINT ''

-- Step 1: Clear related Identity tables first (child tables)
PRINT 'Step 1: Clearing Identity child tables...'

DELETE FROM [Identity].[UserTokens];
PRINT 'UserTokens cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserRoles];  
PRINT 'UserRoles cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserLogins];
PRINT 'UserLogins cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserClaims];
PRINT 'UserClaims cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

-- Step 2: Clear RefreshTokens if they exist
PRINT 'Step 2: Checking for RefreshTokens table...'
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'RefreshTokens' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DELETE FROM [dbo].[RefreshTokens];
    PRINT 'RefreshTokens cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'
END
ELSE
BEGIN
    PRINT 'RefreshTokens table not found (may be embedded in User table)'
END

-- Step 3: Handle business table references (OPTIONAL)
PRINT 'Step 3: Handling business table references...'
PRINT 'WARNING: This will remove user references from booking data!'
PRINT 'Uncomment the following lines if you want to proceed:'
PRINT ''
-- UPDATE [dbo].[RLT_BOOKING] SET Created_By = NULL WHERE Created_By IS NOT NULL;
-- UPDATE [dbo].[RLT_BOOKING] SET Booking_Created_By = NULL WHERE Booking_Created_By IS NOT NULL;
-- PRINT 'Booking user references cleared'

-- Step 4: Now we can safely truncate the User table
PRINT 'Step 4: Truncating User table...'
TRUNCATE TABLE [Identity].[User];
PRINT 'User table truncated successfully!'

PRINT ''
PRINT '=== CLEANUP COMPLETED SUCCESSFULLY ==='

GO

-- =============================================
-- ALTERNATIVE SOLUTIONS (COMMENTED OUT)
-- =============================================

/*
-- =============================================
-- SOLUTION 2: TEMPORARILY DISABLE CONSTRAINTS
-- =============================================
-- WARNING: This is more risky and should be used carefully

PRINT '=== SOLUTION 2: Temporarily disable constraints ==='

-- Disable all foreign key constraints
EXEC sp_msforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all"

-- Truncate the User table
TRUNCATE TABLE [Identity].[User];

-- Re-enable all foreign key constraints
EXEC sp_msforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all"

PRINT 'User table truncated with constraints temporarily disabled'
*/

/*
-- =============================================
-- SOLUTION 3: DROP AND RECREATE CONSTRAINTS
-- =============================================
-- This is the most complex but gives you full control

-- First, script out all foreign key constraints that reference User table
SELECT 
    'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
    'DROP CONSTRAINT [' + fk.name + ']' AS DropConstraintSQL,
    'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
    'ADD CONSTRAINT [' + fk.name + '] FOREIGN KEY ([' + COL_NAME(fkc.parent_object_id, fkc.parent_column_id) + ']) ' +
    'REFERENCES [' + SCHEMA_NAME(pk.schema_id) + '].[' + OBJECT_NAME(pk.object_id) + '] ([' + COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) + '])' +
    CASE 
        WHEN fk.delete_referential_action = 1 THEN ' ON DELETE CASCADE'
        WHEN fk.delete_referential_action = 2 THEN ' ON DELETE SET NULL'
        WHEN fk.delete_referential_action = 3 THEN ' ON DELETE SET DEFAULT'
        ELSE ''
    END AS RecreateConstraintSQL
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.objects pk ON fkc.referenced_object_id = pk.object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')
*/

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

PRINT ''
PRINT '=== VERIFICATION ==='
PRINT 'Run these queries to verify the cleanup:'
PRINT ''
PRINT 'SELECT COUNT(*) as UserCount FROM [Identity].[User];'
PRINT 'SELECT COUNT(*) as UserClaimsCount FROM [Identity].[UserClaims];'
PRINT 'SELECT COUNT(*) as UserLoginsCount FROM [Identity].[UserLogins];'
PRINT 'SELECT COUNT(*) as UserRolesCount FROM [Identity].[UserRoles];'
PRINT 'SELECT COUNT(*) as UserTokensCount FROM [Identity].[UserTokens];'

-- Show current counts
PRINT ''
PRINT 'Current counts after cleanup:'
SELECT COUNT(*) as UserCount FROM [Identity].[User];
SELECT COUNT(*) as UserClaimsCount FROM [Identity].[UserClaims];
SELECT COUNT(*) as UserLoginsCount FROM [Identity].[UserLogins];
SELECT COUNT(*) as UserRolesCount FROM [Identity].[UserRoles];
SELECT COUNT(*) as UserTokensCount FROM [Identity].[UserTokens];
