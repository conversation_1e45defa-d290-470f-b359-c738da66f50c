-- =============================================
-- AGGRESSIVE SOLUTION: Force clear User table by temporarily disabling constraints
-- WARNING: Use this only if the diagnostic approach doesn't work
-- =============================================

USE [CabYaari]
GO

PRINT '=== FORCE CLEARING USER TABLE ==='
PRINT 'WARNING: This temporarily disables ALL foreign key constraints!'
PRINT ''

-- Method 1: Use DELETE instead of TRUNCATE (safer)
PRINT 'Method 1: Using DELETE instead of TRUNCATE...'
DELETE FROM [Identity].[User];
PRINT 'Users deleted: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Reset identity if needed
IF EXISTS (SELECT * FROM sys.identity_columns WHERE object_id = OBJECT_ID('[Identity].[User]'))
BEGIN
    DBCC CHECKIDENT('[Identity].[User]', RESEED, 0)
    PRINT 'Identity column reset'
END

PRINT 'Method 1 completed successfully!'
PRINT ''

-- Verification
SELECT COUNT(*) as UserCount FROM [Identity].[User];

GO

-- =============================================
-- Method 2: Temporarily disable constraints (if Method 1 doesn't work)
-- =============================================

/*
PRINT 'Method 2: Temporarily disabling all constraints...'

-- Disable all foreign key constraints in the database
DECLARE @sql NVARCHAR(MAX) = ''
SELECT @sql = @sql + 'ALTER TABLE [' + SCHEMA_NAME(schema_id) + '].[' + name + '] NOCHECK CONSTRAINT ALL;' + CHAR(13)
FROM sys.tables

EXEC sp_executesql @sql
PRINT 'All constraints disabled'

-- Now truncate the User table
TRUNCATE TABLE [Identity].[User];
PRINT 'User table truncated'

-- Re-enable all foreign key constraints
SET @sql = ''
SELECT @sql = @sql + 'ALTER TABLE [' + SCHEMA_NAME(schema_id) + '].[' + name + '] WITH CHECK CHECK CONSTRAINT ALL;' + CHAR(13)
FROM sys.tables

EXEC sp_executesql @sql
PRINT 'All constraints re-enabled'

PRINT 'Method 2 completed successfully!'
*/

-- =============================================
-- Method 3: Drop and recreate specific constraints (most precise)
-- =============================================

/*
PRINT 'Method 3: Drop and recreate specific User table constraints...'

-- Step 1: Generate and execute DROP statements
DECLARE @dropSql NVARCHAR(MAX) = ''
SELECT @dropSql = @dropSql + 
    'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
    'DROP CONSTRAINT [' + fk.name + '];' + CHAR(13)
FROM sys.foreign_keys fk
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
WHERE fkc.referenced_object_id = OBJECT_ID('[Identity].[User]')

PRINT 'Dropping User table constraints...'
EXEC sp_executesql @dropSql

-- Step 2: Truncate the table
TRUNCATE TABLE [Identity].[User];
PRINT 'User table truncated'

-- Step 3: Recreate the constraints
DECLARE @createSql NVARCHAR(MAX) = ''
-- Note: You would need to store the constraint definitions before dropping them
-- This is more complex and requires additional scripting

PRINT 'Method 3 requires manual constraint recreation - use Method 1 or 2 instead'
*/
