-- =============================================
-- Simple step-by-step solution for User table truncation
-- Run these commands one by one to resolve foreign key constraints
-- =============================================

USE [CabYaari]
GO

-- Step 1: Clear child tables first
DELETE FROM [Identity].[UserTokens];
DELETE FROM [Identity].[UserRoles];  
DELETE FROM [Identity].[UserLogins];
DELETE FROM [Identity].[UserClaims];

-- Step 2: Clear RefreshTokens if exists
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'RefreshTokens')
    DELETE FROM [dbo].[RefreshTokens];

-- Step 3: Now truncate User table
TRUNCATE TABLE [Identity].[User];

-- Verification
SELECT COUNT(*) as UserCount FROM [Identity].[User];
