-- =============================================
-- FINAL SOLUTION: Clear User table based on diagnostic results
-- This includes the missing RefreshToken table
-- =============================================

USE [CabYaari]
GO

PRINT '=== FINAL USER TABLE CLEANUP ==='
PRINT 'Based on diagnostic results, clearing all referencing tables...'
PRINT ''

-- Clear all tables that reference User table (in correct order)
DELETE FROM [Identity].[RefreshToken];
PRINT 'RefreshToken cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserTokens];
PRINT 'UserTokens cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserRoles];
PRINT 'UserRoles cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserLogins];
PRINT 'UserLogins cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

DELETE FROM [Identity].[UserClaims];
PRINT 'UserClaims cleared: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' rows'

PRINT ''
PRINT 'All referencing tables cleared. Now truncating User table...'

-- Now truncate the User table
TRUNCATE TABLE [Identity].[User];
PRINT 'User table truncated successfully!'

PRINT ''
PRINT '=== VERIFICATION ==='
SELECT COUNT(*) as UserCount FROM [Identity].[User];
SELECT COUNT(*) as RefreshTokenCount FROM [Identity].[RefreshToken];
SELECT COUNT(*) as UserClaimsCount FROM [Identity].[UserClaims];
SELECT COUNT(*) as UserLoginsCount FROM [Identity].[UserLogins];
SELECT COUNT(*) as UserRolesCount FROM [Identity].[UserRoles];
SELECT COUNT(*) as UserTokensCount FROM [Identity].[UserTokens];

PRINT ''
PRINT '=== CLEANUP COMPLETED SUCCESSFULLY ==='
